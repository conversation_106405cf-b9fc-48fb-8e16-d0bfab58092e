{"environments": [{"name": "AU-PROD-IP14", "variables": [{"name": "appid", "initial_value": "au.com.kmart", "current_value": "au.com.kmart"}, {"name": "atg-pdp", "initial_value": "add-to-bag-ip14.png", "current_value": "add-to-bag-ip14.png"}, {"name": "catalogue-menu-img", "initial_value": "catalogue-menu.png", "current_value": "catalogue-menu.png"}, {"name": "closebtnimage", "initial_value": "banner-close-updated.png", "current_value": "banner-close-updated.png"}, {"name": "cooker-id", "initial_value": "P_43375509", "current_value": "P_43375509"}, {"name": "deliver-address", "initial_value": "305 238 Flinders", "current_value": "305 238 Flinders"}, {"name": "delivery-addr-x", "initial_value": "54", "current_value": "54"}, {"name": "delivery-addr-y", "initial_value": "314", "current_value": "314"}, {"name": "delivery-address-img", "initial_value": "delivery-address-ip14.png", "current_value": "delivery-address-ip14.png"}, {"name": "device-back-img", "initial_value": "device-back-ip14.png", "current_value": "device-back-ip14.png"}, {"name": "homepage-edit-link-img", "initial_value": "homepage_editbtn-se.png", "current_value": "homepage_editbtn-se.png"}, {"name": "paypal-close-img", "initial_value": "paypalclose-ip14.png", "current_value": "paypalclose-ip14.png"}, {"name": "product-share-img", "initial_value": "product-share-ip14.png", "current_value": "product-share-ip14.png"}, {"name": "pwd", "initial_value": "Wonderbaby@5", "current_value": "Wonderbaby@5"}, {"name": "pwd-op", "initial_value": "Wonderbaby@6", "current_value": "Wonderbaby@6"}, {"name": "searchorder", "initial_value": "447743749", "current_value": "447743749"}, {"name": "store-locator-postcode", "initial_value": "3000", "current_value": "3000"}, {"name": "store-locator-x", "initial_value": "24", "current_value": "24"}, {"name": "store-locator-y", "initial_value": "296", "current_value": "296"}, {"name": "tfappid", "initial_value": "com.apple.TestFlight", "current_value": "com.apple.TestFlight"}, {"name": "uname", "initial_value": "<EMAIL>", "current_value": "<EMAIL>"}, {"name": "uname-op", "initial_value": "<EMAIL>", "current_value": "<EMAIL>"}, {"name": "uname1", "initial_value": "<EMAIL>", "current_value": "<EMAIL>"}, {"name": "uno", "initial_value": "Uno card", "current_value": "Uno card"}]}, {"name": "AU-PROD-SE", "variables": [{"name": "appid", "initial_value": "au.com.kmart", "current_value": "au.com.kmart"}, {"name": "closebtnimage", "initial_value": "banner-close-updated.png", "current_value": "banner-close-updated.png"}, {"name": "deliver-address", "initial_value": "305 238 Flinders", "current_value": "305 238 Flinders"}, {"name": "delivery-address-img", "initial_value": "selectaddress-se.png", "current_value": "selectaddress-se.png"}, {"name": "device-back-img", "initial_value": "deviceback-se.png", "current_value": "deviceback-se.png"}, {"name": "homepage-edit-link-img", "initial_value": "homepage_editbtn-se.png", "current_value": "homepage_editbtn-se.png"}, {"name": "product-share-img", "initial_value": "prodcut-share-se.png", "current_value": "prodcut-share-se.png"}, {"name": "pwd", "initial_value": "Wonderbaby@5", "current_value": "Wonderbaby@5"}, {"name": "tfappid", "initial_value": "com.apple.TestFlight", "current_value": "com.apple.TestFlight"}, {"name": "uname", "initial_value": "<EMAIL>", "current_value": "<EMAIL>"}, {"name": "uname1", "initial_value": "<EMAIL>", "current_value": "<EMAIL>"}, {"name": "uno", "initial_value": "Uno card", "current_value": "Uno card"}]}], "exported_at": "2025-06-20T23:44:48.000Z", "version": "1.0"}