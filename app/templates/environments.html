<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 bg-light p-3">
            <h5>Environments</h5>
            <div class="mb-3">
                <button class="btn btn-primary btn-sm" id="importBtn">
                    <i class="bi bi-upload"></i> Import
                </button>
                <input type="file" id="importFile" accept=".json" style="display: none;">
            </div>
            <div id="environmentsList" class="list-group">
                <!-- Environments will be loaded here -->
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9 p-3">
            <div id="selectedEnvironment">
                <h4 id="environmentTitle">Select an Environment</h4>
                <div id="variablesContainer" style="display: none;">
                    <div class="mb-3">
                        <input type="text" id="filterInput" class="form-control" placeholder="Filter variables...">
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Variable Name</th>
                                    <th>Initial Value</th>
                                    <th>Current Value</th>
                                </tr>
                            </thead>
                            <tbody id="variablesTable">
                                <!-- Variables will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Toast Container -->
<div class="toast-container position-fixed bottom-0 end-0 p-3">
    <div id="toast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <strong class="me-auto">Notification</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body" id="toastBody">
            <!-- Toast message will be here -->
        </div>
    </div>
</div>

<!-- Environment Modal -->
<div class="modal fade" id="environmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="environmentModalTitle">Create Environment</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="environmentForm">
                    <div class="mb-3">
                        <label for="environmentName" class="form-label">Environment Name</label>
                        <input type="text" class="form-control" id="environmentName" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveEnvironmentBtn">Save</button>
            </div>
        </div>
    </div>
</div>

<!-- Variable Modal -->
<div class="modal fade" id="variableModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="variableModalTitle">Create Variable</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="variableForm">
                    <div class="mb-3">
                        <label for="variableName" class="form-label">Variable Name</label>
                        <input type="text" class="form-control" id="variableName" required>
                    </div>
                    <div class="mb-3">
                        <label for="initialValue" class="form-label">Initial Value</label>
                        <input type="text" class="form-control" id="initialValue">
                    </div>
                    <div class="mb-3">
                        <label for="currentValue" class="form-label">Current Value</label>
                        <input type="text" class="form-control" id="currentValue">
                    </div>
                    <div class="mb-3">
                        <label for="variableType" class="form-label">Type</label>
                        <select class="form-select" id="variableType">
                            <option value="default">Default</option>
                            <option value="string">String</option>
                            <option value="number">Number</option>
                            <option value="boolean">Boolean</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveVariableBtn">Save</button>
            </div>
        </div>
    </div>
</div>

<!-- Toast Container -->
<div class="toast-container position-fixed bottom-0 end-0 p-3">
    <div id="toast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <strong class="me-auto">Notification</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body" id="toastBody">
            <!-- Toast message will be here -->
        </div>
    </div>
</div>

<script>
    let currentEnvironment = null;
    let allVariables = [];

    // Load environments on page load
    document.addEventListener('DOMContentLoaded', function() {
        loadEnvironments();
        setupEventListeners();
    });

    function setupEventListeners() {
        // Import button
        document.getElementById('importBtn').addEventListener('click', function() {
            document.getElementById('importFile').click();
        });

        // File import
        document.getElementById('importFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                importEnvironment(file);
            }
        });

        // Filter input
        document.getElementById('filterInput').addEventListener('input', function(e) {
            filterVariables(e.target.value);
        });
    }

    async function loadEnvironments() {
        try {
            const response = await fetch('/api/environments/simple');
            const result = await response.json();

            if (result.success) {
                displayEnvironments(result.data);
            } else {
                showToast('Error loading environments: ' + result.error, 'error');
            }
        } catch (error) {
            showToast('Error loading environments: ' + error.message, 'error');
        }
    }

    function displayEnvironments(environments) {
        const container = document.getElementById('environmentsList');
        container.innerHTML = '';

        if (environments.length === 0) {
            container.innerHTML = '<p class="text-muted">No environments found. Import some environments to get started.</p>';
            return;
        }

        environments.forEach(envName => {
            const item = document.createElement('a');
            item.href = '#';
            item.className = 'list-group-item list-group-item-action';
            item.textContent = envName;
            item.addEventListener('click', function(e) {
                e.preventDefault();
                selectEnvironment(envName);
            });
            container.appendChild(item);
        });
    }

    async function selectEnvironment(environmentName) {
        try {
            // Update UI
            document.querySelectorAll('#environmentsList .list-group-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');

            currentEnvironment = environmentName;
            document.getElementById('environmentTitle').textContent = environmentName;

            // Load variables
            const response = await fetch(`/api/environments/simple/${encodeURIComponent(environmentName)}/variables`);
            const result = await response.json();

            if (result.success) {
                allVariables = result.data;
                displayVariables(allVariables);
                document.getElementById('variablesContainer').style.display = 'block';
            } else {
                showToast('Error loading variables: ' + result.error, 'error');
            }
        } catch (error) {
            showToast('Error loading variables: ' + error.message, 'error');
        }
    }

    function displayVariables(variables) {
        const tbody = document.getElementById('variablesTable');
        tbody.innerHTML = '';

        if (variables.length === 0) {
            tbody.innerHTML = '<tr><td colspan="3" class="text-center text-muted">No variables found</td></tr>';
            return;
        }

        variables.forEach(variable => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td><strong>${escapeHtml(variable.name)}</strong></td>
                <td>${escapeHtml(variable.initial_value || '')}</td>
                <td>${escapeHtml(variable.current_value || '')}</td>
            `;
            tbody.appendChild(row);
        });
    }

    function filterVariables(searchTerm) {
        if (!searchTerm) {
            displayVariables(allVariables);
            return;
        }

        const filtered = allVariables.filter(variable =>
            variable.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (variable.initial_value && variable.initial_value.toLowerCase().includes(searchTerm.toLowerCase())) ||
            (variable.current_value && variable.current_value.toLowerCase().includes(searchTerm.toLowerCase()))
        );

        displayVariables(filtered);
    }

    async function importEnvironment(file) {
        try {
            const text = await file.text();
            const data = JSON.parse(text);

            // Handle different import formats
            let importData = data;
            if (data.success && data.data) {
                importData = data.data;
            }

            console.log('Importing data:', importData);

            const response = await fetch('/api/environments/simple/import', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(importData)
            });

            const result = await response.json();
            console.log('Import result:', result);

            if (result.success) {
                showToast(result.message, 'success');
                await loadEnvironments();

                // Auto-select the imported environment if it's a single environment
                if (result.environment_name) {
                    setTimeout(() => selectEnvironment(result.environment_name), 500);
                }
            } else {
                showToast('Import failed: ' + result.error, 'error');
            }
        } catch (error) {
            showToast('Import failed: ' + error.message, 'error');
        }
    }

    function showToast(message, type = 'info') {
        const toast = document.getElementById('toast');
        const toastBody = document.getElementById('toastBody');

        toastBody.textContent = message;

        // Set toast color based on type
        toast.className = 'toast';
        if (type === 'success') {
            toast.classList.add('bg-success', 'text-white');
        } else if (type === 'error') {
            toast.classList.add('bg-danger', 'text-white');
        } else {
            toast.classList.add('bg-info', 'text-white');
        }

        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

</script>
</body>
</html>