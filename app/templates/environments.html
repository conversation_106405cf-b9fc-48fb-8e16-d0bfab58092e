<div class="container-fluid mt-3">
    <div class="row mb-3">
        <div class="col-md-auto">
            <h4>Environment Management</h4>
        </div>
        <div class="col-md-3">
            <select class="form-select" id="selectedEnvironment">
                <option value="">Select Environment</option>
            </select>
        </div>
        <div class="col-md-auto">
            <button class="btn btn-success" id="setActiveBtn" disabled><i class="bi bi-check-circle"></i> Set Active</button>
        </div>
        <div class="col-md-auto">
            <button class="btn btn-primary" id="createNewEnvironmentBtn"><i class="bi bi-plus-circle"></i> Create New</button>
        </div>
        <div class="col-md-auto">
            <button class="btn btn-warning" id="editEnvironmentBtn" disabled><i class="bi bi-pencil"></i> Edit</button>
        </div>
        <div class="col-md-auto">
            <button class="btn btn-danger" id="deleteEnvironmentBtn" disabled><i class="bi bi-trash"></i> Delete</button>
        </div>
        <div class="col-md-auto">
            <button class="btn btn-info" id="migrateBtn"><i class="bi bi-database-up"></i> Load Data</button>
        </div>
    </div>

    <!-- Active Environment Display -->
    <div class="row mb-3" id="activeEnvironmentDisplay" style="display: none;">
        <div class="col-12">
            <div class="alert alert-success">
                <i class="bi bi-check-circle-fill"></i> Active Environment: <strong id="activeEnvironmentName">None</strong>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Left column for environment list -->
        <div class="col-md-3">
            <h6>Environments</h6>
            <div id="environmentsList" class="list-group">
                <!-- Environments will be loaded here -->
            </div>
        </div>

        <!-- Right column for environment variables -->
        <div class="col-md-9">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <h5 id="environmentNameDisplay">Select an Environment</h5>
                <div>
                    <button class="btn btn-sm btn-primary" id="addNewVariable" disabled>
                        <i class="bi bi-plus-lg"></i> Add Variable
                    </button>
                </div>
            </div>
            <div class="input-group mb-3">
                <span class="input-group-text"><i class="bi bi-search"></i></span>
                <input type="text" class="form-control" placeholder="Filter variables" id="filterVariables">
            </div>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th scope="col">Variable Name</th>
                            <th scope="col">Initial Value</th>
                            <th scope="col">Current Value</th>
                        </tr>
                    </thead>
                    <tbody id="environmentVariablesTableBody">
                        <tr>
                            <td colspan="3" class="text-center text-muted">Select an environment to view variables</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Environment Modal -->
<div class="modal fade" id="environmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="environmentModalTitle">Create Environment</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="environmentForm">
                    <div class="mb-3">
                        <label for="environmentName" class="form-label">Environment Name</label>
                        <input type="text" class="form-control" id="environmentName" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveEnvironmentBtn">Save</button>
            </div>
        </div>
    </div>
</div>

<!-- Variable Modal -->
<div class="modal fade" id="variableModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="variableModalTitle">Create Variable</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="variableForm">
                    <div class="mb-3">
                        <label for="variableName" class="form-label">Variable Name</label>
                        <input type="text" class="form-control" id="variableName" required>
                    </div>
                    <div class="mb-3">
                        <label for="initialValue" class="form-label">Initial Value</label>
                        <input type="text" class="form-control" id="initialValue">
                    </div>
                    <div class="mb-3">
                        <label for="currentValue" class="form-label">Current Value</label>
                        <input type="text" class="form-control" id="currentValue">
                    </div>
                    <div class="mb-3">
                        <label for="variableType" class="form-label">Type</label>
                        <select class="form-select" id="variableType">
                            <option value="default">Default</option>
                            <option value="string">String</option>
                            <option value="number">Number</option>
                            <option value="boolean">Boolean</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveVariableBtn">Save</button>
            </div>
        </div>
    </div>
</div>

<!-- Toast Container -->
<div class="toast-container position-fixed bottom-0 end-0 p-3">
    <div id="toast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <strong class="me-auto">Notification</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body" id="toastBody">
            <!-- Toast message will be here -->
        </div>
    </div>
</div>

<!-- Environment Management JavaScript - Updated 2025-06-21 -->
<script>
document.addEventListener('DOMContentLoaded', function () {
    // DOM Elements
    const selectedEnvironmentDropdown = document.getElementById('selectedEnvironment');
    const environmentNameDisplay = document.getElementById('environmentNameDisplay');
    const environmentVariablesTableBody = document.getElementById('environmentVariablesTableBody');
    const filterInput = document.getElementById('filterVariables');
    const addNewVariableButton = document.getElementById('addNewVariable');
    const createNewEnvironmentButton = document.getElementById('createNewEnvironmentBtn');
    const deleteEnvironmentBtn = document.getElementById('deleteEnvironmentBtn');
    const editEnvironmentBtn = document.getElementById('editEnvironmentBtn');
    const setActiveBtn = document.getElementById('setActiveBtn');
    const migrateBtn = document.getElementById('migrateBtn');
    const environmentsList = document.getElementById('environmentsList');
    const activeEnvironmentDisplay = document.getElementById('activeEnvironmentDisplay');
    const activeEnvironmentName = document.getElementById('activeEnvironmentName');

    // Modals
    const environmentModal = new bootstrap.Modal(document.getElementById('environmentModal'));
    const variableModal = new bootstrap.Modal(document.getElementById('variableModal'));

    // State
    let currentSelectedEnvId = null;
    let activeEnvironmentId = null;
    let environmentsData = [];
    let currentEditingVariableId = null;
    let currentEditingEnvironmentId = null;

    // Utility Functions
    function showToast(message, type = 'info') {
        const toast = document.getElementById('toast');
        const toastBody = document.getElementById('toastBody');

        toastBody.textContent = message;

        // Set toast color based on type
        toast.className = 'toast';
        if (type === 'success') {
            toast.classList.add('bg-success', 'text-white');
        } else if (type === 'error') {
            toast.classList.add('bg-danger', 'text-white');
        } else {
            toast.classList.add('bg-info', 'text-white');
        }

        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
    }

    async function apiCall(url, method = 'GET', body = null) {
        const options = {
            method,
            headers: {
                'Content-Type': 'application/json',
            },
        };
        if (body) {
            options.body = JSON.stringify(body);
        }
        try {
            const response = await fetch(url, options);
            const responseData = await response.json();
            if (!response.ok) {
                const errorMsg = responseData.error || `API Error: ${response.status}`;
                showToast(errorMsg, 'error');
                console.error('API Error:', response.status, responseData);
                return { success: false, data: responseData, status: response.status };
            }
            return { success: true, data: responseData, status: response.status };
        } catch (error) {
            showToast('Network or server error: ' + error.message, 'error');
            console.error('Fetch Error:', error);
            return { success: false, error: error.message, data: { error: error.message} };
        }
    }

    // Environment Management Functions
    async function loadEnvironments() {
        try {
            const result = await apiCall('/api/env/environments');
            if (result.success) {
                // The API response has the structure: { success: true, data: [...] }
                // The apiCall function returns: { success: true, data: { success: true, data: [...] } }
                // So we need to access result.data.data to get the actual environments array
                const apiResponse = result.data;
                if (apiResponse && Array.isArray(apiResponse.data)) {
                    environmentsData = apiResponse.data;
                } else {
                    console.warn('API returned non-array data:', apiResponse);
                    environmentsData = [];
                }
                console.log('Loaded environments:', environmentsData);
                populateEnvironmentsList();

                // Load active environment
                const activeResult = await apiCall('/api/env/environments/active');
                if (activeResult.success && activeResult.data && activeResult.data.data) {
                    const activeEnv = activeResult.data.data;
                    activeEnvironmentId = activeEnv.id;
                    console.log('Active environment ID:', activeEnvironmentId);
                    updateActiveEnvironmentDisplay(activeEnv);

                    // Ensure we select the active environment after a short delay
                    setTimeout(() => {
                        selectEnvironment(activeEnvironmentId);
                    }, 100);
                } else {
                    console.log('No active environment found');
                    activeEnvironmentDisplay.style.display = 'none';
                }
            } else {
                console.error('API call failed:', result);
                environmentsData = [];
                populateEnvironmentsList();
                showToast('Error loading environments: ' + (result.error || 'Unknown error'), 'error');
            }
        } catch (error) {
            console.error('Error loading environments:', error);
            environmentsData = [];
            populateEnvironmentsList();
            showToast('Error loading environments: ' + error.message, 'error');
        }
    }

    function populateEnvironmentsList() {
        environmentsList.innerHTML = '';
        selectedEnvironmentDropdown.innerHTML = '<option value="">Select Environment</option>';

        // Ensure environmentsData is an array
        if (!Array.isArray(environmentsData)) {
            console.warn('environmentsData is not an array:', environmentsData);
            environmentsData = [];
        }

        if (environmentsData.length === 0) {
            environmentsList.innerHTML = '<p class="text-muted p-2">No environments found. Click "Create New" to add one.</p>';
            return;
        }

        environmentsData.forEach(env => {
            // Add to dropdown
            const option = document.createElement('option');
            option.value = env.id;
            option.textContent = env.name;
            selectedEnvironmentDropdown.appendChild(option);

            // Add to list
            const listItem = document.createElement('a');
            listItem.href = '#';
            listItem.className = 'list-group-item list-group-item-action';
            listItem.dataset.envId = env.id;

            if (env.is_active) {
                listItem.innerHTML = `<i class="bi bi-check-circle-fill text-success me-2"></i>${env.name}`;
                listItem.classList.add('active');
            } else {
                listItem.textContent = env.name;
            }

            listItem.addEventListener('click', function(e) {
                e.preventDefault();
                selectEnvironment(env.id);
            });

            environmentsList.appendChild(listItem);
        });
    }

    function selectEnvironment(envId) {
        console.log('Selecting environment ID:', envId);
        console.log('Available environments:', environmentsData);
        currentSelectedEnvId = envId;

        // Update UI
        selectedEnvironmentDropdown.value = envId;

        // Update list selection
        environmentsList.querySelectorAll('.list-group-item').forEach(item => {
            item.classList.remove('list-group-item-primary');
            if (item.dataset.envId == envId) {
                item.classList.add('list-group-item-primary');
            }
        });

        // Update environment name display
        const env = environmentsData.find(e => e.id == envId);
        console.log('Found environment:', env);
        if (env) {
            environmentNameDisplay.textContent = env.name;
            loadVariables(envId);

            // Enable/disable buttons
            setActiveBtn.disabled = false;
            editEnvironmentBtn.disabled = false;
            deleteEnvironmentBtn.disabled = false;
            addNewVariableButton.disabled = false;
        } else {
            console.log('Environment not found, showing default message');
            environmentNameDisplay.textContent = 'Select an Environment';
            environmentVariablesTableBody.innerHTML = '<tr><td colspan="3" class="text-center text-muted">Select an environment to view variables</td></tr>';

            // Disable buttons
            setActiveBtn.disabled = true;
            editEnvironmentBtn.disabled = true;
            deleteEnvironmentBtn.disabled = true;
            addNewVariableButton.disabled = true;
        }
    }

    function updateActiveEnvironmentDisplay(activeEnv) {
        if (activeEnv) {
            activeEnvironmentName.textContent = activeEnv.name;
            activeEnvironmentDisplay.style.display = 'block';
        } else {
            activeEnvironmentDisplay.style.display = 'none';
        }
    }

    // Variable Management Functions
    async function loadVariables(envId) {
        console.log('Loading variables for environment ID:', envId);
        try {
            // Try the new API first
            let result = await apiCall(`/api/env/environments/${envId}/variables`);
            console.log('New API result:', result);

            // If new API fails or returns no data, try the old API
            if (!result.success || !result.data || (Array.isArray(result.data) && result.data.length === 0)) {
                console.log('Trying old API endpoint...');
                result = await apiCall(`/api/environments/${envId}/variables`);
                console.log('Old API result:', result);

                // For old API, the data is directly in the response
                if (Array.isArray(result) && result.length > 0) {
                    console.log('Found data in old API format');
                    displayVariables(result);
                    return;
                }
            }

            if (result.success && result.data) {
                console.log('API call successful, checking data structure...');

                // Check if result.data has a nested data property
                if (result.data.data) {
                    console.log('Found nested data structure');
                    const variables = result.data.data;
                    console.log('Variables from nested data:', variables);
                    displayVariables(variables);
                } else if (Array.isArray(result.data)) {
                    console.log('Data is directly an array');
                    displayVariables(result.data);
                } else {
                    console.log('Unexpected data structure:', result.data);
                    environmentVariablesTableBody.innerHTML = '<tr><td colspan="3" class="text-center text-muted">Unexpected data format</td></tr>';
                }
            } else {
                console.error('Failed to load variables:', result);
                environmentVariablesTableBody.innerHTML = '<tr><td colspan="3" class="text-center text-muted">No variables found. Click "Add Variable" to create one.</td></tr>';
            }
        } catch (error) {
            console.error('Error loading variables:', error);
            showToast('Error loading variables: ' + error.message, 'error');
        }
    }

    function displayVariables(variables) {
        console.log('displayVariables called with:', variables);
        console.log('Variables type:', typeof variables);
        console.log('Variables is array:', Array.isArray(variables));
        console.log('Variables length:', variables ? variables.length : 'undefined');

        if (!environmentVariablesTableBody) {
            console.error('environmentVariablesTableBody element not found');
            return;
        }

        environmentVariablesTableBody.innerHTML = '';

        if (!variables || !Array.isArray(variables) || variables.length === 0) {
            console.log('No variables to display');
            environmentVariablesTableBody.innerHTML = '<tr><td colspan="3" class="text-center text-muted">No variables found. Click "Add Variable" to create one.</td></tr>';
            return;
        }

        console.log('Displaying', variables.length, 'variables');

        // Build the HTML string for all rows
        let rowsHtml = '';
        variables.forEach((variable, index) => {
            console.log(`Variable ${index}:`, variable);
            rowsHtml += `
                <tr>
                    <td>${escapeHtml(variable.name)}</td>
                    <td>${escapeHtml(variable.initial_value || '')}</td>
                    <td>${escapeHtml(variable.current_value || '')}</td>
                </tr>
            `;
        });

        // Set all rows at once
        environmentVariablesTableBody.innerHTML = rowsHtml;
        console.log('Variables display completed');
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Event Handlers
    selectedEnvironmentDropdown.addEventListener('change', function() {
        const envId = this.value;
        if (envId) {
            selectEnvironment(envId);
        } else {
            currentSelectedEnvId = null;
            environmentNameDisplay.textContent = 'Select an Environment';
            environmentVariablesTableBody.innerHTML = '<tr><td colspan="3" class="text-center text-muted">Select an environment to view variables</td></tr>';

            // Disable buttons
            setActiveBtn.disabled = true;
            editEnvironmentBtn.disabled = true;
            deleteEnvironmentBtn.disabled = true;
            addNewVariableButton.disabled = true;
        }
    });

    setActiveBtn.addEventListener('click', async function() {
        if (!currentSelectedEnvId) {
            showToast('Please select an environment first.', 'info');
            return;
        }

        try {
            const result = await apiCall(`/api/env/environments/${currentSelectedEnvId}/activate`, 'POST');
            if (result.success) {
                activeEnvironmentId = currentSelectedEnvId;
                const env = environmentsData.find(e => e.id == currentSelectedEnvId);
                updateActiveEnvironmentDisplay(env);
                populateEnvironmentsList(); // Refresh to show active status
                showToast(`Environment '${env.name}' is now active.`, 'success');
            }
        } catch (error) {
            showToast('Error setting active environment: ' + error.message, 'error');
        }
    });

    createNewEnvironmentButton.addEventListener('click', function() {
        currentEditingEnvironmentId = null;
        document.getElementById('environmentModalTitle').textContent = 'Create Environment';
        document.getElementById('environmentName').value = '';
        environmentModal.show();
    });

    editEnvironmentBtn.addEventListener('click', function() {
        if (!currentSelectedEnvId) {
            showToast('Please select an environment first.', 'info');
            return;
        }

        const env = environmentsData.find(e => e.id == currentSelectedEnvId);
        if (env) {
            currentEditingEnvironmentId = currentSelectedEnvId;
            document.getElementById('environmentModalTitle').textContent = 'Edit Environment';
            document.getElementById('environmentName').value = env.name;
            environmentModal.show();
        }
    });

    deleteEnvironmentBtn.addEventListener('click', async function() {
        if (!currentSelectedEnvId) {
            showToast('Please select an environment first.', 'info');
            return;
        }

        const env = environmentsData.find(e => e.id == currentSelectedEnvId);
        if (env && confirm(`Are you sure you want to delete environment "${env.name}" and all its variables?`)) {
            try {
                const result = await apiCall(`/api/env/environments/${currentSelectedEnvId}`, 'DELETE');
                if (result.success) {
                    showToast(`Environment "${env.name}" deleted successfully.`, 'success');
                    currentSelectedEnvId = null;
                    await loadEnvironments();
                }
            } catch (error) {
                showToast('Error deleting environment: ' + error.message, 'error');
            }
        }
    });

    migrateBtn.addEventListener('click', async function() {
        if (confirm('This will load environment data from JSON files. Continue?')) {
            try {
                const result = await apiCall('/api/env/migrate', 'POST');
                if (result.success) {
                    showToast(`Successfully migrated ${result.data.migrated_count} environments.`, 'success');
                    await loadEnvironments();
                } else {
                    showToast('Migration failed: ' + (result.data.error || 'Unknown error'), 'error');
                }
            } catch (error) {
                showToast('Error during migration: ' + error.message, 'error');
            }
        }
    });

    // Modal event handlers
    document.getElementById('saveEnvironmentBtn').addEventListener('click', async function() {
        const name = document.getElementById('environmentName').value.trim();
        if (!name) {
            showToast('Environment name is required.', 'error');
            return;
        }

        try {
            let result;
            if (currentEditingEnvironmentId) {
                // Edit existing environment
                result = await apiCall(`/api/env/environments/${currentEditingEnvironmentId}`, 'PUT', { name });
            } else {
                // Create new environment
                result = await apiCall('/api/env/environments', 'POST', { name });
            }

            if (result.success) {
                showToast(`Environment ${currentEditingEnvironmentId ? 'updated' : 'created'} successfully.`, 'success');
                environmentModal.hide();
                await loadEnvironments();

                if (!currentEditingEnvironmentId && result.data.data) {
                    selectEnvironment(result.data.data.id);
                }
            }
        } catch (error) {
            showToast('Error saving environment: ' + error.message, 'error');
        }
    });

    addNewVariableButton.addEventListener('click', function() {
        if (!currentSelectedEnvId) {
            showToast('Please select an environment first.', 'info');
            return;
        }

        currentEditingVariableId = null;
        document.getElementById('variableModalTitle').textContent = 'Create Variable';
        document.getElementById('variableName').value = '';
        document.getElementById('initialValue').value = '';
        document.getElementById('currentValue').value = '';
        document.getElementById('variableType').value = 'default';
        variableModal.show();
    });

    document.getElementById('saveVariableBtn').addEventListener('click', async function() {
        const name = document.getElementById('variableName').value.trim();
        const initialValue = document.getElementById('initialValue').value.trim();
        const currentValue = document.getElementById('currentValue').value.trim();
        const variableType = document.getElementById('variableType').value;

        if (!name) {
            showToast('Variable name is required.', 'error');
            return;
        }

        try {
            let result;
            if (currentEditingVariableId) {
                // Edit existing variable
                result = await apiCall(`/api/env/variables/${currentEditingVariableId}`, 'PUT', {
                    name, initial_value: initialValue, current_value: currentValue, variable_type: variableType
                });
            } else {
                // Create new variable
                result = await apiCall(`/api/env/environments/${currentSelectedEnvId}/variables`, 'POST', {
                    name, initial_value: initialValue, current_value: currentValue, variable_type: variableType
                });
            }

            if (result.success) {
                showToast(`Variable ${currentEditingVariableId ? 'updated' : 'created'} successfully.`, 'success');
                variableModal.hide();
                loadVariables(currentSelectedEnvId);
            }
        } catch (error) {
            showToast('Error saving variable: ' + error.message, 'error');
        }
    });

    // Global functions for inline onclick handlers
    window.editVariable = function(variableId) {
        const variable = environmentsData.find(env => env.id == currentSelectedEnvId)?.variables?.find(v => v.id == variableId);
        if (!variable) {
            // Try to find in current displayed variables
            const rows = environmentVariablesTableBody.querySelectorAll('tr');
            for (let row of rows) {
                if (row.children[0] && row.children[0].textContent.includes(variableId)) {
                    variable = {
                        id: variableId,
                        name: row.children[0].textContent,
                        initial_value: row.children[1].textContent,
                        current_value: row.children[2].textContent,
                        variable_type: row.children[3].textContent
                    };
                    break;
                }
            }
        }

        if (variable) {
            currentEditingVariableId = variableId;
            document.getElementById('variableModalTitle').textContent = 'Edit Variable';
            document.getElementById('variableName').value = variable.name;
            document.getElementById('initialValue').value = variable.initial_value || '';
            document.getElementById('currentValue').value = variable.current_value || '';
            document.getElementById('variableType').value = variable.variable_type || 'default';
            variableModal.show();
        }
    };

    window.deleteVariable = async function(variableId) {
        if (confirm('Are you sure you want to delete this variable?')) {
            try {
                const result = await apiCall(`/api/env/variables/${variableId}`, 'DELETE');
                if (result.success) {
                    showToast('Variable deleted successfully.', 'success');
                    loadVariables(currentSelectedEnvId);
                }
            } catch (error) {
                showToast('Error deleting variable: ' + error.message, 'error');
            }
        }
    };

    // Filter functionality
    if (filterInput) {
        filterInput.addEventListener('keyup', function() {
            const filterText = this.value.toLowerCase();
            const rows = environmentVariablesTableBody.querySelectorAll('tr');

            rows.forEach(row => {
                if (row.children.length > 1) { // Skip header and empty rows
                    const variableName = row.children[0].textContent.toLowerCase();
                    if (variableName.includes(filterText)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                }
            });
        });
    }

    // Initialize the page
    loadEnvironments();
});

</script>