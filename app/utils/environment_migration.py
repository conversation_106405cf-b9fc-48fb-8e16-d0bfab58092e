#!/usr/bin/env python3
"""
Environment Migration Script
Loads environment data from JSON files into the new database schema
"""

import json
import os
import sys
import logging
from datetime import datetime

# Add the parent directory to the path so we can import from utils
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.database import (
    create_environment, get_environment_by_name, 
    bulk_create_variables, set_active_environment
)

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def load_environment_from_json(file_path):
    """Load environment data from a JSON file"""
    try:
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        if not data.get('success', False):
            logger.error(f"JSON file {file_path} indicates failure")
            return None
        
        env_data = data.get('data', {})
        env_name = env_data.get('name')
        variables = env_data.get('variables', [])
        
        if not env_name:
            logger.error(f"No environment name found in {file_path}")
            return None
        
        logger.info(f"Loaded environment '{env_name}' with {len(variables)} variables from {file_path}")
        return {
            'name': env_name,
            'variables': variables,
            'exported_at': env_data.get('exported_at'),
            'version': env_data.get('version')
        }
    
    except Exception as e:
        logger.error(f"Error loading {file_path}: {str(e)}")
        return None


def migrate_environment(env_data):
    """Migrate a single environment to the new database schema"""
    try:
        env_name = env_data['name']
        variables = env_data['variables']
        
        # Check if environment already exists
        existing_env = get_environment_by_name(env_name)
        if existing_env:
            logger.info(f"Environment '{env_name}' already exists (ID: {existing_env['id']})")
            environment_id = existing_env['id']
        else:
            # Create new environment
            environment_id = create_environment(env_name)
            if not environment_id:
                logger.error(f"Failed to create environment '{env_name}'")
                return False
            logger.info(f"Created environment '{env_name}' with ID {environment_id}")
        
        # Prepare variables for bulk creation
        variables_to_create = []
        for var in variables:
            variables_to_create.append({
                'name': var.get('name'),
                'initial_value': var.get('initial_value', ''),
                'current_value': var.get('current_value', var.get('initial_value', '')),
                'type': var.get('type', 'default')
            })
        
        # Create variables
        created_count = bulk_create_variables(environment_id, variables_to_create)
        logger.info(f"Created {created_count} variables for environment '{env_name}'")
        
        return True
    
    except Exception as e:
        logger.error(f"Error migrating environment '{env_data.get('name', 'Unknown')}': {str(e)}")
        return False


def migrate_all_environments(json_files):
    """Migrate all environments from the provided JSON files"""
    try:
        logger.info("Starting environment migration...")
        
        migrated_environments = []
        
        for file_path in json_files:
            if not os.path.exists(file_path):
                logger.warning(f"File not found: {file_path}")
                continue
            
            logger.info(f"Processing file: {file_path}")
            env_data = load_environment_from_json(file_path)
            
            if env_data:
                success = migrate_environment(env_data)
                if success:
                    migrated_environments.append(env_data['name'])
                    logger.info(f"Successfully migrated environment: {env_data['name']}")
                else:
                    logger.error(f"Failed to migrate environment: {env_data['name']}")
        
        logger.info(f"Migration completed. Migrated {len(migrated_environments)} environments:")
        for env_name in migrated_environments:
            logger.info(f"  - {env_name}")
        
        # Set the first environment as active if any were migrated
        if migrated_environments:
            first_env = get_environment_by_name(migrated_environments[0])
            if first_env:
                set_active_environment(first_env['id'])
                logger.info(f"Set '{migrated_environments[0]}' as the active environment")
        
        return len(migrated_environments)
    
    except Exception as e:
        logger.error(f"Error during migration: {str(e)}")
        return 0


def main():
    """Main migration function"""
    # Define the JSON files to migrate
    base_path = "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest"
    json_files = [
        os.path.join(base_path, "environment_export_2025-06-20.json"),
        os.path.join(base_path, "environment_export_2025-06-20 (1).json"),
        os.path.join(base_path, "environment_export_2025-06-20 (2).json")
    ]
    
    logger.info("Environment Migration Script")
    logger.info("=" * 50)
    
    # Run migration
    migrated_count = migrate_all_environments(json_files)
    
    if migrated_count > 0:
        logger.info(f"Migration successful! Migrated {migrated_count} environments.")
    else:
        logger.error("Migration failed or no environments were migrated.")
    
    return migrated_count


if __name__ == "__main__":
    main()
