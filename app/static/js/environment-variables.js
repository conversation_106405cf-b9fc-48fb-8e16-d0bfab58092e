/**
 * Environment Variables Management
 * Handles CRUD operations for environment variables in the Settings tab
 */

class EnvironmentVariablesManager {
    constructor() {
        this.currentEditingVariable = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadEnvironmentVariables();
    }

    bindEvents() {
        // Add variable button
        document.getElementById('addEnvironmentVariableBtn')?.addEventListener('click', () => {
            this.showAddVariableModal();
        });

        // Refresh button
        document.getElementById('refreshEnvironmentVariablesBtn')?.addEventListener('click', () => {
            this.loadEnvironmentVariables();
        });

        // Save variable button
        document.getElementById('saveEnvironmentVariableBtn')?.addEventListener('click', () => {
            this.saveEnvironmentVariable();
        });

        // Form validation
        document.getElementById('envVarName')?.addEventListener('input', (e) => {
            this.validateVariableName(e.target);
        });
    }

    async loadEnvironmentVariables() {
        try {
            const response = await fetch('/api/environment_variables');
            const data = await response.json();

            if (data.status === 'success') {
                this.renderEnvironmentVariables(data.variables);
            } else {
                console.error('Failed to load environment variables:', data.message);
                this.showError('Failed to load environment variables: ' + data.message);
            }
        } catch (error) {
            console.error('Error loading environment variables:', error);
            this.showError('Error loading environment variables: ' + error.message);
        }
    }

    renderEnvironmentVariables(variables) {
        const tableBody = document.getElementById('environmentVariablesTableBody');
        const noVariablesDiv = document.getElementById('noEnvironmentVariables');

        if (!tableBody) return;

        tableBody.innerHTML = '';

        if (variables.length === 0) {
            noVariablesDiv.style.display = 'block';
            return;
        }

        noVariablesDiv.style.display = 'none';

        variables.forEach(variable => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <code>${this.escapeHtml(variable.name)}</code>
                </td>
                <td>
                    <span class="text-truncate d-inline-block" style="max-width: 200px;" title="${this.escapeHtml(variable.value)}">
                        ${this.escapeHtml(variable.value)}
                    </span>
                </td>
                <td>
                    <span class="text-muted">${this.escapeHtml(variable.description || 'No description')}</span>
                </td>
                <td>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="environmentVariablesManager.editVariable('${variable.name}')" title="Edit">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="environmentVariablesManager.deleteVariable('${variable.name}')" title="Delete">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            tableBody.appendChild(row);
        });
    }

    showAddVariableModal() {
        this.currentEditingVariable = null;
        document.getElementById('envVarModalTitle').textContent = 'Add Environment Variable';
        document.getElementById('envVarName').value = '';
        document.getElementById('envVarValue').value = '';
        document.getElementById('envVarDescription').value = '';
        document.getElementById('envVarName').disabled = false;

        const modal = new bootstrap.Modal(document.getElementById('environmentVariableModal'));
        modal.show();
    }

    async editVariable(variableName) {
        try {
            const response = await fetch('/api/environment_variables');
            const data = await response.json();

            if (data.status === 'success') {
                const variable = data.variables.find(v => v.name === variableName);
                if (variable) {
                    this.currentEditingVariable = variableName;
                    document.getElementById('envVarModalTitle').textContent = 'Edit Environment Variable';
                    document.getElementById('envVarName').value = variable.name;
                    document.getElementById('envVarValue').value = variable.value;
                    document.getElementById('envVarDescription').value = variable.description || '';
                    document.getElementById('envVarName').disabled = true; // Don't allow name changes

                    const modal = new bootstrap.Modal(document.getElementById('environmentVariableModal'));
                    modal.show();
                }
            }
        } catch (error) {
            console.error('Error loading variable for editing:', error);
            this.showError('Error loading variable for editing: ' + error.message);
        }
    }

    async saveEnvironmentVariable() {
        const name = document.getElementById('envVarName').value.trim();
        const value = document.getElementById('envVarValue').value.trim();
        const description = document.getElementById('envVarDescription').value.trim();

        if (!name || !value) {
            this.showError('Variable name and value are required');
            return;
        }

        if (!this.isValidVariableName(name)) {
            this.showError('Variable name must contain only alphanumeric characters and underscores');
            return;
        }

        try {
            const response = await fetch('/api/environment_variables', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    name: name,
                    value: value,
                    description: description || null
                })
            });

            const data = await response.json();

            if (data.status === 'success') {
                this.showSuccess(data.message);
                this.loadEnvironmentVariables();
                
                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('environmentVariableModal'));
                modal.hide();
            } else {
                this.showError(data.message);
            }
        } catch (error) {
            console.error('Error saving environment variable:', error);
            this.showError('Error saving environment variable: ' + error.message);
        }
    }

    async deleteVariable(variableName) {
        if (!confirm(`Are you sure you want to delete the environment variable "${variableName}"?`)) {
            return;
        }

        try {
            const response = await fetch(`/api/environment_variables/${encodeURIComponent(variableName)}`, {
                method: 'DELETE'
            });

            const data = await response.json();

            if (data.status === 'success') {
                this.showSuccess(data.message);
                this.loadEnvironmentVariables();
            } else {
                this.showError(data.message);
            }
        } catch (error) {
            console.error('Error deleting environment variable:', error);
            this.showError('Error deleting environment variable: ' + error.message);
        }
    }

    validateVariableName(input) {
        const name = input.value.trim();
        const isValid = this.isValidVariableName(name);
        
        if (name && !isValid) {
            input.classList.add('is-invalid');
        } else {
            input.classList.remove('is-invalid');
        }
    }

    isValidVariableName(name) {
        return /^[a-zA-Z_][a-zA-Z0-9_]*$/.test(name);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    showSuccess(message) {
        // You can integrate with your existing notification system
        console.log('Success:', message);
        // For now, just show an alert - you can replace this with your app's notification system
        if (window.app && window.app.logAction) {
            window.app.logAction('success', message);
        } else {
            alert('Success: ' + message);
        }
    }

    showError(message) {
        // You can integrate with your existing notification system
        console.error('Error:', message);
        // For now, just show an alert - you can replace this with your app's notification system
        if (window.app && window.app.logAction) {
            window.app.logAction('error', message);
        } else {
            alert('Error: ' + message);
        }
    }
}

// Initialize the environment variables manager when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.environmentVariablesManager = new EnvironmentVariablesManager();
});
