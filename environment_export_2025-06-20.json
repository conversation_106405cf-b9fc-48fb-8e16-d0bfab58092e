{"data": {"exported_at": "2025-06-21T00:03:27.493565", "name": "AU-PROD-IP14", "variables": [{"created_at": "2025-06-09 00:43:40", "current_value": "au.com.kmart", "id": 22, "initial_value": "au.com.kmart", "name": "appid", "type": "string", "updated_at": "2025-06-09 00:43:40"}, {"created_at": "2025-06-09 09:06:38", "current_value": "add-to-bag-ip14.png", "id": 49, "initial_value": "add-to-bag-ip14.png", "name": "atg-pdp", "type": "default", "updated_at": "2025-06-09 09:06:38"}, {"created_at": "2025-06-09 12:15:16", "current_value": "catalogue-menu.png", "id": 50, "initial_value": "catalogue-menu.png", "name": "catalogue-menu-img", "type": "default", "updated_at": "2025-06-09 12:15:16"}, {"created_at": "2025-06-09 00:43:40", "current_value": "banner-close-updated.png", "id": 23, "initial_value": "banner-close-updated.png", "name": "closebtnimage", "type": "string", "updated_at": "2025-06-09 00:43:40"}, {"created_at": "2025-06-14 22:49:39", "current_value": "P_43375509", "id": 51, "initial_value": "P_43375509", "name": "cooker-id", "type": "default", "updated_at": "2025-06-14 22:49:39"}, {"created_at": "2025-06-09 00:43:40", "current_value": "305 238 Flinders", "id": 24, "initial_value": "305 238 Flinders", "name": "deliver-address", "type": "string", "updated_at": "2025-06-09 00:43:40"}, {"created_at": "2025-06-09 08:31:53", "current_value": "54", "id": 47, "initial_value": "54", "name": "delivery-addr-x", "type": "default", "updated_at": "2025-06-09 08:31:53"}, {"created_at": "2025-06-09 08:32:13", "current_value": "314", "id": 48, "initial_value": "314", "name": "delivery-addr-y", "type": "default", "updated_at": "2025-06-09 08:32:13"}, {"created_at": "2025-06-09 01:47:37", "current_value": "delivery-address-ip14.png", "id": 38, "initial_value": "delivery-address-ip14.png", "name": "delivery-address-img", "type": "default", "updated_at": "2025-06-09 01:47:37"}, {"created_at": "2025-06-09 01:11:15", "current_value": "device-back-ip14.png", "id": 35, "initial_value": "device-back-ip14.png", "name": "device-back-img", "type": "default", "updated_at": "2025-06-09 01:11:15"}, {"created_at": "2025-06-09 00:43:41", "current_value": "homepage_editbtn-se.png", "id": 27, "initial_value": "homepage_editbtn-se.png", "name": "homepage-edit-link-img", "type": "string", "updated_at": "2025-06-09 00:43:41"}, {"created_at": "2025-06-09 01:15:47", "current_value": "paypalclose-ip14.png", "id": 36, "initial_value": "paypalclose-ip14.png", "name": "paypal-close-img", "type": "default", "updated_at": "2025-06-09 01:15:47"}, {"created_at": "2025-06-09 00:59:52", "current_value": "product-share-ip14.png", "id": 34, "initial_value": "product-share-ip14.png", "name": "product-share-img", "type": "default", "updated_at": "2025-06-09 00:59:52"}, {"created_at": "2025-06-09 00:43:41", "current_value": "Wonderbaby@5", "id": 29, "initial_value": "Wonderbaby@5", "name": "pwd", "type": "default", "updated_at": "2025-06-09 00:43:41"}, {"created_at": "2025-06-09 02:05:36", "current_value": "Wonderbaby@6", "id": 40, "initial_value": "Wonderbaby@6", "name": "pwd-op", "type": "default", "updated_at": "2025-06-09 02:05:36"}, {"created_at": "2025-06-09 08:20:14", "current_value": "447743749", "id": 44, "initial_value": "447743749", "name": "searchorder", "type": "default", "updated_at": "2025-06-09 08:20:14"}, {"created_at": "2025-06-09 04:33:29", "current_value": "3000", "id": 43, "initial_value": "3000", "name": "store-locator-postcode", "type": "default", "updated_at": "2025-06-09 04:33:29"}, {"created_at": "2025-06-09 04:23:22", "current_value": "24", "id": 41, "initial_value": "24", "name": "store-locator-x", "type": "default", "updated_at": "2025-06-09 04:23:22"}, {"created_at": "2025-06-09 04:23:47", "current_value": "296", "id": 42, "initial_value": "296", "name": "store-locator-y", "type": "default", "updated_at": "2025-06-09 04:23:47"}, {"created_at": "2025-06-09 00:43:41", "current_value": "com.apple.TestFlight", "id": 30, "initial_value": "com.apple.TestFlight", "name": "tfappid", "type": "string", "updated_at": "2025-06-09 00:43:41"}, {"created_at": "2025-06-09 00:43:41", "current_value": "<EMAIL>", "id": 31, "initial_value": "<EMAIL>", "name": "uname", "type": "default", "updated_at": "2025-06-09 00:43:41"}, {"created_at": "2025-06-09 02:05:13", "current_value": "<EMAIL>", "id": 39, "initial_value": "<EMAIL>", "name": "uname-op", "type": "default", "updated_at": "2025-06-09 02:05:13"}, {"created_at": "2025-06-09 00:43:41", "current_value": "<EMAIL>", "id": 32, "initial_value": "<EMAIL>", "name": "uname1", "type": "string", "updated_at": "2025-06-09 00:43:41"}, {"created_at": "2025-06-09 00:43:41", "current_value": "Uno card", "id": 33, "initial_value": "Uno card", "name": "uno", "type": "default", "updated_at": "2025-06-15 01:28:54"}], "version": "1.0"}, "success": true}