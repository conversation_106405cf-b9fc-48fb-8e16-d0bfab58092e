{"data": {"exported_at": "2025-06-21T00:03:51.088586", "name": "NZ-PROD-IP14", "variables": [{"created_at": "2025-06-15 22:13:08", "current_value": "nz.com.kmart", "id": 52, "initial_value": "nz.com.kmart", "name": "appid", "type": "default", "updated_at": "2025-06-15 23:26:58"}, {"created_at": "2025-06-15 22:13:08", "current_value": "add-to-bag-ip14.png", "id": 53, "initial_value": "add-to-bag-ip14.png", "name": "atg-pdp", "type": "default", "updated_at": "2025-06-15 22:13:08"}, {"created_at": "2025-06-15 22:13:08", "current_value": "catalogue-menu.png", "id": 54, "initial_value": "catalogue-menu.png", "name": "catalogue-menu-img", "type": "default", "updated_at": "2025-06-15 22:13:08"}, {"created_at": "2025-06-15 22:13:08", "current_value": "banner-close-updated.png", "id": 55, "initial_value": "banner-close-updated.png", "name": "closebtnimage", "type": "string", "updated_at": "2025-06-15 22:13:08"}, {"created_at": "2025-06-15 22:13:08", "current_value": "P_43375509", "id": 56, "initial_value": "P_43375509", "name": "cooker-id", "type": "default", "updated_at": "2025-06-15 22:13:08"}, {"created_at": "2025-06-15 22:13:08", "current_value": "6 Quay Street", "id": 57, "initial_value": "6 Quay Street", "name": "deliver-address", "type": "default", "updated_at": "2025-06-15 23:31:30"}, {"created_at": "2025-06-15 22:13:08", "current_value": "54", "id": 58, "initial_value": "54", "name": "delivery-addr-x", "type": "default", "updated_at": "2025-06-15 22:13:08"}, {"created_at": "2025-06-15 22:13:08", "current_value": "314", "id": 59, "initial_value": "314", "name": "delivery-addr-y", "type": "default", "updated_at": "2025-06-15 22:13:08"}, {"created_at": "2025-06-15 22:13:08", "current_value": "delivery-address-ip14.png", "id": 60, "initial_value": "delivery-address-ip14.png", "name": "delivery-address-img", "type": "default", "updated_at": "2025-06-15 22:13:08"}, {"created_at": "2025-06-15 22:13:08", "current_value": "device-back-ip14.png", "id": 61, "initial_value": "device-back-ip14.png", "name": "device-back-img", "type": "default", "updated_at": "2025-06-15 22:13:08"}, {"created_at": "2025-06-15 22:13:08", "current_value": "homepage_editbtn-se.png", "id": 62, "initial_value": "homepage_editbtn-se.png", "name": "homepage-edit-link-img", "type": "string", "updated_at": "2025-06-15 22:13:08"}, {"created_at": "2025-06-15 22:13:08", "current_value": "paypalclose-ip14.png", "id": 63, "initial_value": "paypalclose-ip14.png", "name": "paypal-close-img", "type": "default", "updated_at": "2025-06-15 22:13:08"}, {"created_at": "2025-06-15 22:13:08", "current_value": "product-share-ip14.png", "id": 64, "initial_value": "product-share-ip14.png", "name": "product-share-img", "type": "default", "updated_at": "2025-06-15 22:13:08"}, {"created_at": "2025-06-15 22:13:08", "current_value": "Wonderbaby@5", "id": 65, "initial_value": "Wonderbaby@5", "name": "pwd", "type": "default", "updated_at": "2025-06-15 22:13:08"}, {"created_at": "2025-06-15 22:13:08", "current_value": "Wonderbaby@6", "id": 66, "initial_value": "Wonderbaby@6", "name": "pwd-op", "type": "default", "updated_at": "2025-06-15 22:13:08"}, {"created_at": "2025-06-15 22:13:08", "current_value": "470096160", "id": 67, "initial_value": "470096160", "name": "searchorder", "type": "default", "updated_at": "2025-06-15 23:30:24"}, {"created_at": "2025-06-15 22:13:08", "current_value": "3000", "id": 68, "initial_value": "3000", "name": "store-locator-postcode", "type": "default", "updated_at": "2025-06-15 22:13:08"}, {"created_at": "2025-06-15 22:13:08", "current_value": "24", "id": 69, "initial_value": "24", "name": "store-locator-x", "type": "default", "updated_at": "2025-06-15 22:13:08"}, {"created_at": "2025-06-15 22:13:08", "current_value": "296", "id": 70, "initial_value": "296", "name": "store-locator-y", "type": "default", "updated_at": "2025-06-15 22:13:08"}, {"created_at": "2025-06-15 22:13:08", "current_value": "com.apple.TestFlight", "id": 71, "initial_value": "com.apple.TestFlight", "name": "tfappid", "type": "string", "updated_at": "2025-06-15 22:13:08"}, {"created_at": "2025-06-15 22:13:08", "current_value": "<EMAIL>", "id": 72, "initial_value": "<EMAIL>", "name": "uname", "type": "default", "updated_at": "2025-06-15 23:27:42"}, {"created_at": "2025-06-15 22:13:08", "current_value": "Uno card", "id": 75, "initial_value": "Uno card", "name": "uno", "type": "default", "updated_at": "2025-06-15 22:13:08"}], "version": "1.0"}, "success": true}