{"data": {"exported_at": "2025-06-21T00:03:45.119719", "name": "AU-PROD-SE", "variables": [{"created_at": "2025-06-07 11:19:42", "current_value": "au.com.kmart", "id": 4, "initial_value": "au.com.kmart", "name": "appid", "type": "string", "updated_at": "2025-06-07 11:19:42"}, {"created_at": "2025-06-07 11:21:16", "current_value": "banner-close-updated.png", "id": 5, "initial_value": "banner-close-updated.png", "name": "closebtnimage", "type": "string", "updated_at": "2025-06-07 11:21:16"}, {"created_at": "2025-06-08 02:15:41", "current_value": "305 238 Flinders", "id": 19, "initial_value": "305 238 Flinders", "name": "deliver-address", "type": "string", "updated_at": "2025-06-08 02:15:41"}, {"created_at": "2025-06-08 02:17:55", "current_value": "selectaddress-se.png", "id": 20, "initial_value": "selectaddress-se.png", "name": "delivery-address-img", "type": "string", "updated_at": "2025-06-08 02:17:55"}, {"created_at": "2025-06-07 11:36:54", "current_value": "deviceback-se.png", "id": 6, "initial_value": "deviceback-se.png", "name": "device-back-img", "type": "string", "updated_at": "2025-06-07 11:36:54"}, {"created_at": "2025-06-08 02:11:19", "current_value": "homepage_editbtn-se.png", "id": 18, "initial_value": "homepage_editbtn-se.png", "name": "homepage-edit-link-img", "type": "string", "updated_at": "2025-06-08 02:11:19"}, {"created_at": "2025-06-07 12:15:50", "current_value": "prodcut-share-se.png", "id": 7, "initial_value": "prodcut-share-se.png", "name": "product-share-img", "type": "string", "updated_at": "2025-06-07 12:15:50"}, {"created_at": "2025-06-08 01:47:54", "current_value": "Wonderbaby@5", "id": 15, "initial_value": "Wonderbaby@5", "name": "pwd", "type": "default", "updated_at": "2025-06-08 01:47:54"}, {"created_at": "2025-06-07 11:17:29", "current_value": "com.apple.TestFlight", "id": 3, "initial_value": "com.apple.TestFlight", "name": "tfappid", "type": "string", "updated_at": "2025-06-07 11:17:29"}, {"created_at": "2025-06-08 01:56:10", "current_value": "<EMAIL>", "id": 16, "initial_value": "<EMAIL>", "name": "uname", "type": "default", "updated_at": "2025-06-08 01:56:10"}, {"created_at": "2025-06-08 02:32:23", "current_value": "<EMAIL>", "id": 21, "initial_value": "<EMAIL>", "name": "uname1", "type": "string", "updated_at": "2025-06-08 02:32:23"}, {"created_at": "2025-06-08 01:56:33", "current_value": "Uno card", "id": 17, "initial_value": "Uno card", "name": "uno", "type": "default", "updated_at": "2025-06-08 01:56:33"}], "version": "1.0"}, "success": true}