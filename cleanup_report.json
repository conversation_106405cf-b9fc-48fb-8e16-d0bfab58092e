{"summary": {"total_files": 864, "used_files": 159, "unused_files": 45, "duplicate_groups": 2, "temp_directories": 11, "old_db_files": 4}, "unused_files": ["MobileApp_AutoTest.egg-info/PKG-INFO", "app/data/automation.db", "app/data/global_values.db", "app/data/global_values_port_8081.db", "app/data/global_values_port_8082.db", "app/data/global_values_port_8083.db", "app/data/global_values_port_8088.db", "app/data/settings.db", "app/data/settings_port_8081.db", "app/data/settings_port_8082.db", "app/data/settings_port_8083.db", "app/data/settings_port_8088.db", "app/data/test_execution.db", "app/database.db", "app/debug_images/__init__.py", "app/routes/__init__.py", "app/suites/__init__.py", "app/utils/__init__.py", "app/utils/openReport.js", "app/utils/reportGenerator.js", "appium_server.log", "data/test_execution.db", "data/test_execution_port_8081.db", "data/test_execution_port_8082.db", "data/test_execution_port_8083.db", "data/test_execution_port_8088.db", "docs/html/api/endpoints.html", "docs/html/api/summary.html", "docs/html/architecture/overview.html", "docs/html/codebase_structure.html", "docs/html/guides/getting_started.html", "docs/html/index.html", "docs/html/modules/summary.html", "docs/html/overview/project_overview.html", "docs/html/troubleshooting/common_issues.html", "docs/template.html", "image_comparison/report/report.html", "multi_device_test.html", "reports/assets/report.css", "reports/assets/report.js", "reports/example_usage.js", "reports/generate_report.js", "reports/report_template.html", "sonar-project.properties", "temp_text_detection/frozen_east_text_detection.pb"], "duplicates": {"9127226366536332204": ["app/debug_images/__init__.py", "app/static/screenshots/__init__.py", "app/suites/__init__.py"], "-8592995150060084514": ["reports/report_template.html", "app/templates/custom_report_template.html"]}, "temp_directories": ["__pycache__", "comparison_results", "debug_images", "docs/analysis", "docs/html", "output", "screenshots/session_1", "screenshots/session_2", "temp", "temp_screenshots", "temp_text_detection"], "old_db_files": ["data/test_execution_port_8081.db", "data/test_execution_port_8082.db", "data/test_execution_port_8083.db", "data/test_execution_port_8088.db"]}